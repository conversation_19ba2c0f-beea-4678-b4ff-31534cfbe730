import datetime
import sys
import os
import time

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from steps.tools import qwen3_sli_xt,qwen3


prompt = """
#目的#

请你作为语义分析专家和养老保险的业务专家，完成以下任务：
1、根据问题描述完成问题转写
2、根据问题描述选出最匹配的的主体类型
3、识别问题的核心意图。

#数据集定义#
主体类型集合：["企事业单位","城乡居民","灵活就业人员","企业职工"]

#问题描述#
问题描述："我交了8年公积金，也办理养老保险，如何开公司啊，，


#主体类型匹配规则#
1、优先从问题描述分析出主体类型，并从主体类型集合中选出最匹配的主体类型。**如果问题描述没有主体类型相关信息或者主体类型与主体类型集合匹配度低时，不做假设，不推测可能的主体类型，主体类型为空**

#意图识别规则#
1、分析问题描述中的关键动作词和目标对象，识别用户的核心需求
2、忽略前置条件和背景信息，专注于用户真正想要解决的核心问题
3、将复杂的问题描述简化为清晰的意图表达
4、意图应该是具体的、可操作的服务需求
5、意图格式参考：公积金办理、查询龙华区服务点明细信息、咨询国家社会保险公共服务平台养老保险关系转移的软件操作、退休年龄计算、了解最低工资标准、社保缴费查询、医保报销流程、失业保险申请等

#返回格式#
1、需要把主体类型和用户意图合并一起输出，禁止返回分析过程
2、主体类型如果是多个，用逗号分开返回。
3、按如下json格式返回结果，不要出现json关键词
{"主体类型":"主体类型","用户意图":"用户意图"}
"""
messages=[{
    "role":"user",
    "content":prompt
}]

# 记录开始时间
start_time = time.time()

print("开始执行意图识别分析...")
result = qwen3(messages)

# 记录结束时间
end_time = time.time()

# 计算执行时间
execution_time = end_time - start_time

print(f"分析结果: {result}")
print(f"执行时间: {execution_time:.2f} 秒")

